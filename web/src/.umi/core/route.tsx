// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/login","layout":false,"id":"1"},"2":{"path":"/login-next","layout":false,"id":"2"},"3":{"path":"/chat/share","layout":false,"id":"3"},"4":{"path":"/next-chats/share","layout":false,"id":"4"},"5":{"path":"/agent/share","layout":false,"id":"5"},"6":{"path":"/home","layout":false,"redirect":"/knowledge","id":"6"},"7":{"path":"/knowledge","parentId":"@@/global-layout","id":"7"},"8":{"path":"/knowledge","parentId":"@@/global-layout","id":"8"},"9":{"path":"dataset","parentId":"8","id":"9"},"10":{"path":"","parentId":"9","id":"10"},"11":{"path":"chunk","parentId":"9","id":"11"},"12":{"path":"configuration","parentId":"8","id":"12"},"13":{"path":"testing","parentId":"8","id":"13"},"14":{"path":"knowledgeGraph","parentId":"8","id":"14"},"15":{"path":"/chat","parentId":"@@/global-layout","id":"15"},"16":{"path":"/file","parentId":"@@/global-layout","id":"16"},"17":{"path":"/flow","parentId":"@@/global-layout","id":"17"},"18":{"path":"/agent-list","parentId":"@@/global-layout","id":"18"},"19":{"path":"/flow/:id","parentId":"@@/global-layout","id":"19"},"20":{"path":"/search","parentId":"@@/global-layout","id":"20"},"21":{"path":"/document/:id","layout":false,"id":"21"},"22":{"path":"/*","layout":false,"id":"22"},"23":{"path":"","layout":false,"parentId":"24","id":"23","originPath":"/"},"24":{"path":"/","isWrapper":true,"layout":false,"id":"24"},"25":{"path":"/","parentId":"23","id":"25"},"26":{"path":"/datasets","layout":false,"id":"26"},"27":{"path":"/datasets","parentId":"26","id":"27"},"28":{"path":"/next-chats","layout":false,"id":"28"},"29":{"path":"/next-chats","parentId":"28","id":"29"},"30":{"path":"/next-chat/:id","layout":false,"id":"30"},"31":{"path":"/next-searches","layout":false,"id":"31"},"32":{"path":"/next-searches","parentId":"31","id":"32"},"33":{"path":"/next-search/:id","layout":false,"id":"33"},"34":{"path":"/next-search/share","layout":false,"id":"34"},"35":{"path":"/agents","layout":false,"id":"35"},"36":{"path":"/agents","parentId":"35","id":"36"},"37":{"path":"/agent-log-page/:id","layout":false,"id":"37"},"38":{"path":"/agent/:id","layout":false,"id":"38"},"39":{"path":"/agent-templates","layout":false,"id":"39"},"40":{"path":"/files","layout":false,"id":"40"},"41":{"path":"/files","parentId":"40","id":"41"},"42":{"path":"/dataset","layout":false,"id":"42"},"43":{"path":"/dataset","redirect":"/dataset/dataset","parentId":"42","id":"43"},"44":{"path":"/dataset","layout":false,"id":"44"},"45":{"path":"/dataset/dataset/:id","parentId":"44","id":"45"},"46":{"path":"/dataset/setting/:id","parentId":"44","id":"46"},"47":{"path":"/dataset/testing/:id","parentId":"44","id":"47"},"48":{"path":"/dataset/knowledge-graph/:id","parentId":"44","id":"48"},"49":{"path":"/dataset/dataset-overview/:id","parentId":"44","id":"49"},"50":{"path":"/dataset/dataset-setting/:id","parentId":"44","id":"50"},"51":{"path":"/dataflow-result/:id","layout":false,"id":"51"},"52":{"path":"/chunk/parsed/chunks","layout":false,"id":"52"},"53":{"path":"/chunk","layout":false,"id":"53"},"54":{"path":"/chunk","parentId":"53","id":"54"},"55":{"path":"/chunk/chunk/:id","parentId":"54","id":"55"},"56":{"path":"/chunk/result/:id","parentId":"54","id":"56"},"57":{"path":"/chunk","layout":false,"id":"57"},"58":{"path":"/profile-setting","layout":false,"id":"58"},"59":{"path":"/profile-setting","redirect":"/profile-setting/profile","parentId":"58","id":"59"},"60":{"path":"/profile-setting/profile","parentId":"58","id":"60"},"61":{"path":"/profile-setting/team","parentId":"58","id":"61"},"62":{"path":"/profile-setting/plan","parentId":"58","id":"62"},"63":{"path":"/profile-setting/model","parentId":"58","id":"63"},"64":{"path":"/profile-setting/prompt","parentId":"58","id":"64"},"65":{"path":"/profile-setting/mcp","parentId":"58","id":"65"},"66":{"path":"/user-setting","layout":false,"id":"66"},"67":{"path":"/user-setting","redirect":"/user-setting/profile","parentId":"66","id":"67"},"68":{"path":"/user-setting/profile","parentId":"66","id":"68"},"69":{"path":"/user-setting/locale","parentId":"66","id":"69"},"70":{"path":"/user-setting/password","parentId":"66","id":"70"},"71":{"path":"/user-setting/model","parentId":"66","id":"71"},"72":{"path":"/user-setting/team","parentId":"66","id":"72"},"73":{"path":"/user-setting/system","parentId":"66","id":"73"},"74":{"path":"/user-setting/api","parentId":"66","id":"74"},"75":{"path":"/user-setting/mcp","parentId":"66","id":"75"},"76":{"path":"/data-flows","layout":false,"id":"76"},"77":{"path":"/data-flows","parentId":"76","id":"77"},"78":{"path":"/data-flow/:id","layout":false,"id":"78"},"@@/global-layout":{"id":"@@/global-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import(/* webpackChunkName: "p__login__index" */'@/pages/login/index.tsx')),
'2': React.lazy(() => import(/* webpackChunkName: "p__login-next__index" */'@/pages/login-next/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__chat__share__index" */'@/pages/chat/share/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__next-chats__share__index" */'@/pages/next-chats/share/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__agent__share__index" */'@/pages/agent/share/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "layouts__index" */'@/layouts/index.tsx')),
'7': React.lazy(() => import(/* webpackChunkName: "p__knowledge__index" */'@/pages/knowledge/index.tsx')),
'8': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__index" */'@/pages/add-knowledge/index.tsx')),
'9': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-dataset__index" */'@/pages/add-knowledge/components/knowledge-dataset/index.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-file__index" */'@/pages/add-knowledge/components/knowledge-file/index.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-chunk__index" */'@/pages/add-knowledge/components/knowledge-chunk/index.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-setting__index" */'@/pages/add-knowledge/components/knowledge-setting/index.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-testing__index" */'@/pages/add-knowledge/components/knowledge-testing/index.tsx')),
'14': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-graph__index" */'@/pages/add-knowledge/components/knowledge-graph/index.tsx')),
'15': React.lazy(() => import(/* webpackChunkName: "p__chat__index" */'@/pages/chat/index.tsx')),
'16': React.lazy(() => import(/* webpackChunkName: "p__file-manager__index" */'@/pages/file-manager/index.tsx')),
'17': React.lazy(() => import(/* webpackChunkName: "p__flow__list__index" */'@/pages/flow/list/index.tsx')),
'18': React.lazy(() => import(/* webpackChunkName: "p__agents__index" */'@/pages/agents/index.tsx')),
'19': React.lazy(() => import(/* webpackChunkName: "p__flow__index" */'@/pages/flow/index.tsx')),
'20': React.lazy(() => import(/* webpackChunkName: "p__search__index" */'@/pages/search/index.tsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__document-viewer__index" */'@/pages/document-viewer/index.tsx')),
'22': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.jsx')),
'23': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'24': React.lazy(() => import(/* webpackChunkName: "wrappers__auth" */'@/wrappers/auth.tsx')),
'25': React.lazy(() => import(/* webpackChunkName: "p__home__index" */'@/pages/home/<USER>')),
'26': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'27': React.lazy(() => import(/* webpackChunkName: "p__datasets__index" */'@/pages/datasets/index.tsx')),
'28': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'29': React.lazy(() => import(/* webpackChunkName: "p__next-chats__index" */'@/pages/next-chats/index.tsx')),
'30': React.lazy(() => import(/* webpackChunkName: "p__next-chats__chat__index" */'@/pages/next-chats/chat/index.tsx')),
'31': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'32': React.lazy(() => import(/* webpackChunkName: "p__next-searches__index" */'@/pages/next-searches/index.tsx')),
'33': React.lazy(() => import(/* webpackChunkName: "p__next-search__index" */'@/pages/next-search/index.tsx')),
'34': React.lazy(() => import(/* webpackChunkName: "p__next-search__share__index" */'@/pages/next-search/share/index.tsx')),
'35': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'36': React.lazy(() => import(/* webpackChunkName: "p__agents__index" */'@/pages/agents/index.tsx')),
'37': React.lazy(() => import(/* webpackChunkName: "p__agents__agent-log-page" */'@/pages/agents/agent-log-page.tsx')),
'38': React.lazy(() => import(/* webpackChunkName: "p__agent__index" */'@/pages/agent/index.tsx')),
'39': React.lazy(() => import(/* webpackChunkName: "p__agents__agent-templates" */'@/pages/agents/agent-templates.tsx')),
'40': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'41': React.lazy(() => import(/* webpackChunkName: "p__files__index" */'@/pages/files/index.tsx')),
'42': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'43': React.lazy(() => import('./EmptyRoute')),
'44': React.lazy(() => import(/* webpackChunkName: "p__dataset__index" */'@/pages/dataset/index.tsx')),
'45': React.lazy(() => import(/* webpackChunkName: "p__dataset__dataset__index" */'@/pages/dataset/dataset/index.tsx')),
'46': React.lazy(() => import(/* webpackChunkName: "p__dataset__setting__index" */'@/pages/dataset/setting/index.tsx')),
'47': React.lazy(() => import(/* webpackChunkName: "p__dataset__testing__index" */'@/pages/dataset/testing/index.tsx')),
'48': React.lazy(() => import(/* webpackChunkName: "p__dataset__knowledge-graph__index" */'@/pages/dataset/knowledge-graph/index.tsx')),
'49': React.lazy(() => import(/* webpackChunkName: "p__dataset__dataset-overview__index" */'@/pages/dataset/dataset-overview/index.tsx')),
'50': React.lazy(() => import(/* webpackChunkName: "p__dataset__dataset-setting__index" */'@/pages/dataset/dataset-setting/index.tsx')),
'51': React.lazy(() => import(/* webpackChunkName: "p__dataflow-result__index" */'@/pages/dataflow-result/index.tsx')),
'52': React.lazy(() => import(/* webpackChunkName: "p__chunk__parsed-result__add-knowledge__components__knowledge-chunk__index" */'@/pages/chunk/parsed-result/add-knowledge/components/knowledge-chunk/index.tsx')),
'53': React.lazy(() => import('./EmptyRoute')),
'54': React.lazy(() => import(/* webpackChunkName: "p__chunk__index" */'@/pages/chunk/index.tsx')),
'55': React.lazy(() => import(/* webpackChunkName: "p__chunk__chunk-result__index" */'@/pages/chunk/chunk-result/index.tsx')),
'56': React.lazy(() => import(/* webpackChunkName: "p__chunk__result-view__index" */'@/pages/chunk/result-view/index.tsx')),
'57': React.lazy(() => import(/* webpackChunkName: "p__chunk__index" */'@/pages/chunk/index.tsx')),
'58': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__index" */'@/pages/profile-setting/index.tsx')),
'59': React.lazy(() => import('./EmptyRoute')),
'60': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__profile__index" */'@/pages/profile-setting/profile/index.tsx')),
'61': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__team__index" */'@/pages/profile-setting/team/index.tsx')),
'62': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__plan__index" */'@/pages/profile-setting/plan/index.tsx')),
'63': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__model__index" */'@/pages/profile-setting/model/index.tsx')),
'64': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__prompt__index" */'@/pages/profile-setting/prompt/index.tsx')),
'65': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__mcp__index" */'@/pages/profile-setting/mcp/index.tsx')),
'66': React.lazy(() => import(/* webpackChunkName: "p__user-setting__index" */'@/pages/user-setting/index.tsx')),
'67': React.lazy(() => import('./EmptyRoute')),
'68': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-profile__index" */'@/pages/user-setting/setting-profile/index.tsx')),
'69': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-locale__index" */'@/pages/user-setting/setting-locale/index.tsx')),
'70': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-password__index" */'@/pages/user-setting/setting-password/index.tsx')),
'71': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-model__index" */'@/pages/user-setting/setting-model/index.tsx')),
'72': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-team__index" */'@/pages/user-setting/setting-team/index.tsx')),
'73': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-system__index" */'@/pages/user-setting/setting-system/index.tsx')),
'74': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-api__index" */'@/pages/user-setting/setting-api/index.tsx')),
'75': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__mcp__index" */'@/pages/profile-setting/mcp/index.tsx')),
'76': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'77': React.lazy(() => import(/* webpackChunkName: "p__data-flows__index" */'@/pages/data-flows/index.tsx')),
'78': React.lazy(() => import(/* webpackChunkName: "p__data-flow__index" */'@/pages/data-flow/index.tsx')),
'@@/global-layout': React.lazy(() => import(/* webpackChunkName: "layouts__index" */'/Users/<USER>/Desktop/openAI/ragflow/web/src/layouts/index.tsx')),
},
  };
}
