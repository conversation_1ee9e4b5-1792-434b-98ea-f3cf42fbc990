# RAGFlow 白标化功能快速启动指南

## 概述

本指南提供RAGFlow白标化功能的快速启动步骤，帮助您快速部署自定义品牌的RAGFlow系统。

## 前提条件

- Docker 和 Docker Compose 已安装
- Git 已安装
- 企业Logo和品牌资源文件已准备

## 快速启动步骤

### 1. 克隆项目
```bash
git clone https://github.com/infiniflow/ragflow.git
cd ragflow
```

### 2. 准备品牌资源
```bash
# 创建品牌资源目录
mkdir -p web/public/brand

# 复制您的Logo文件
cp /path/to/your/logo.svg web/public/custom-logo.svg

# 复制您的Favicon文件
cp /path/to/your/favicon.ico web/public/favicon.ico

# 复制其他品牌资源
cp /path/to/your/brand/* web/public/brand/
```

### 3. 配置白标化设置
```bash
# 复制环境变量文件
cp docker/.env.white-label.example .env.white-label

# 编辑环境变量
vim .env.white-label
```

修改以下关键配置：
```env
WHITE_LABEL_COMPANY_NAME=您的公司名称
WHITE_LABEL_PRIMARY_COLOR=#您的品牌主色
WHITE_LABEL_SECONDARY_COLOR=#您的品牌辅助色
WHITE_LABEL_CONTACT_EMAIL=<EMAIL>
WHITE_LABEL_CONTACT_PHONE=+86-xxx-xxxx-xxxx
```

### 4. 配置前端设置
```bash
# 编辑前端配置文件
vim web/src/conf.json
```

修改配置文件：
```json
{
  "appName": "您的应用名称",
  "whiteLabel": {
    "enabled": true,
    "companyName": "您的公司名称",
    "logo": "/custom-logo.svg",
    "favicon": "/favicon.ico",
    "theme": {
      "primaryColor": "#您的品牌主色",
      "secondaryColor": "#您的品牌辅助色"
    },
    "footer": {
      "text": "© 2024 您的公司名称. 保留所有权利.",
      "links": [
        { "text": "隐私政策", "url": "/privacy" },
        { "text": "使用条款", "url": "/terms" }
      ]
    },
    "contact": {
      "email": "<EMAIL>",
      "phone": "+86-xxx-xxxx-xxxx"
    }
  }
}
```

### 5. 启动服务
```bash
# 给脚本执行权限
chmod +x docker/build-white-label.sh docker/start-white-label.sh

# 构建并启动服务
./docker/start-white-label.sh
```

### 6. 验证部署
```bash
# 检查服务状态
docker ps | grep ragflow-white-label

# 访问应用
open http://localhost
```

## 常用命令

### 服务管理
```bash
# 启动服务
./docker/start-white-label.sh

# 停止服务
./docker/stop-white-label.sh

# 查看日志
docker-compose -f docker/docker-compose-white-label.yml logs -f

# 重启服务
docker-compose -f docker/docker-compose-white-label.yml restart
```

### 构建管理
```bash
# 重新构建镜像
./docker/build-white-label.sh

# 清理未使用的镜像
docker image prune -f
```

## 配置验证

### 1. 检查品牌标识
- 访问 http://localhost
- 确认Logo显示正确
- 确认页面标题显示公司名称

### 2. 检查主题色彩
- 确认按钮和链接使用品牌主色
- 确认界面元素使用品牌辅助色

### 3. 检查页脚信息
- 确认版权文本正确
- 确认联系信息显示正确

## 故障排除

### 1. 服务启动失败
```bash
# 检查端口占用
lsof -i :80
lsof -i :443

# 查看错误日志
docker-compose -f docker/docker-compose-white-label.yml logs ragflow-white-label
```

### 2. Logo不显示
```bash
# 检查文件是否存在
ls -la web/public/custom-logo.svg
ls -la web/public/favicon.ico

# 检查文件权限
chmod 644 web/public/custom-logo.svg
chmod 644 web/public/favicon.ico
```

### 3. 主题不生效
```bash
# 检查配置文件
cat web/src/conf.json

# 检查环境变量
cat .env.white-label
```

## 自定义选项

### 1. 高级主题配置
您可以在 `web/src/white-label-config.ts` 中添加更多主题配置：

```typescript
export const whiteLabelConfig = {
  // 基础配置
  enabled: true,
  companyName: '您的公司名称',
  
  // 高级主题配置
  theme: {
    primaryColor: '#1890ff',
    secondaryColor: '#52c41a',
    fontFamily: 'Inter, sans-serif',
    borderRadius: '6px',
    spacing: '8px',
  },
  
  // 自定义CSS
  customCSS: `
    .custom-class {
      background: linear-gradient(45deg, #primaryColor, #secondaryColor);
    }
  `,
};
```

### 2. 多语言支持
如需支持多语言，可以创建语言文件：

```bash
# 创建语言目录
mkdir -p web/src/locales

# 创建中文语言文件
echo '{
  "common": {
    "companyName": "您的公司名称",
    "welcome": "欢迎使用"
  }
}' > web/src/locales/zh.json

# 创建英文语言文件
echo '{
  "common": {
    "companyName": "Your Company Name",
    "welcome": "Welcome"
  }
}' > web/src/locales/en.json
```

### 3. 自定义页面
您可以创建自定义页面来增强品牌体验：

```bash
# 创建自定义页面目录
mkdir -p web/src/pages/custom

# 创建关于我们页面
cat > web/src/pages/custom/About.tsx << 'EOF'
import React from 'react';
import { WhiteLabelLogo } from '@/components/white-label';

const About = () => {
  return (
    <div className="about-page">
      <WhiteLabelLogo width={80} height={80} />
      <h1>关于我们</h1>
      <p>您的公司介绍...</p>
    </div>
  );
};

export default About;
EOF
```

## 性能优化

### 1. 资源优化
```bash
# 压缩图片资源
find web/public -name "*.png" -exec pngquant --ext=.png --force 256 {} \;
find web/public -name "*.jpg" -exec jpegoptim --max-quality=85 {} \;

# 优化SVG文件
svgo web/public/custom-logo.svg
```

### 2. 构建优化
```bash
# 设置环境变量
export NODE_ENV=production

# 构建优化版本
npm run build:optimized
```

### 3. 缓存配置
在 `docker/nginx-white-label.conf` 中添加缓存配置：

```nginx
# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
}
```

## 安全加固

### 1. SSL/TLS配置
```bash
# 创建SSL证书目录
mkdir -p docker/nginx/ssl

# 生成自签名证书（仅用于测试）
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout docker/nginx/ssl/key.pem \
  -out docker/nginx/ssl/cert.pem \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=YourCompany/CN=localhost"
```

### 2. 访问控制
在 `docker/nginx-white-label.conf` 中添加访问控制：

```nginx
# 限制管理界面访问
location /admin {
    allow ***********/24;
    deny all;
}

# 限制API访问
location /api {
    limit_req zone=api burst=20 nodelay;
}
```

### 3. 安全头配置
```nginx
# 添加安全头
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';";
add_header X-Content-Type-Options "nosniff";
add_header X-Frame-Options "DENY";
add_header X-XSS-Protection "1; mode=block";
```

## 监控和日志

### 1. 健康检查
```bash
# 检查服务状态
curl http://localhost/health

# 检查API状态
curl http://localhost:8080/api/health
```

### 2. 日志监控
```bash
# 查看实时日志
docker-compose -f docker/docker-compose-white-label.yml logs -f ragflow-white-label

# 查看错误日志
docker-compose -f docker/docker-compose-white-label.yml logs ragflow-white-label | grep ERROR
```

### 3. 性能监控
```bash
# 监控资源使用
docker stats ragflow-white-label

# 监控网络连接
docker network inspect ragflow-white-label-network
```

## 备份和恢复

### 1. 配置备份
```bash
# 备份配置文件
tar -czf config-backup-$(date +%Y%m%d).tar.gz \
  web/src/conf.json \
  .env.white-label \
  docker/
```

### 2. 数据备份
```bash
# 备份数据库
docker exec ragflow-db pg_dump -U ragflow ragflow > db-backup-$(date +%Y%m%d).sql

# 备份用户数据
docker cp ragflow-white-label:/app/data ./data-backup-$(date +%Y%m%d)
```

### 3. 恢复数据
```bash
# 恢复配置
tar -xzf config-backup-YYYYMMDD.tar.gz

# 恢复数据库
docker exec -i ragflow-db psql -U ragflow ragflow < db-backup-YYYYMMDD.sql

# 恢复用户数据
docker cp ./data-backup-YYYYMMDD ragflow-white-label:/app/data
```

## 下一步

完成快速启动后，您可以：

1. **深入定制**: 查看 `WHITE_LABEL_FEATURE_SUMMARY.md` 了解更多定制选项
2. **集成功能**: 集成MinEU插件和其他企业功能
3. **部署生产**: 参考 `docker/WHITE_LABEL_DEPLOYMENT.md` 进行生产环境部署
4. **监控维护**: 设置监控和告警系统

## 技术支持

如需技术支持，请联系：
- 邮箱：<EMAIL>
- 电话：+86-xxx-xxxx-xxxx
- 文档：`WHITE_LABEL_FEATURE_SUMMARY.md`

---

祝您使用愉快！
